{"presets": {"optimized_768": {"name": "Optimized 768D (Recommended)", "description": "Optimized configuration with 768 dimensions for best performance/memory balance", "config": {"embedder_provider": "gemini", "embedder_model": "text-embedding-004", "embedder_dims": 768, "base_collection_name": "mem0_gemini", "llm_provider": "gemini", "llm_model": "gemini-2.5-flash", "llm_temperature": 0.1, "llm_max_tokens": 1000, "memory_relevance_threshold": 0.2, "max_memories_to_inject": 3, "memory_search_limit": 10, "auto_store_messages": true, "enable_fallback_search": true}}, "high_precision_1536": {"name": "High Precision 1536D", "description": "Higher dimensional embeddings for better semantic understanding", "config": {"embedder_provider": "gemini", "embedder_model": "gemini-embedding-001", "embedder_dims": 1536, "base_collection_name": "mem0_gemini", "llm_provider": "gemini", "llm_model": "gemini-2.5-flash", "llm_temperature": 0.1, "llm_max_tokens": 1000, "memory_relevance_threshold": 0.3, "max_memories_to_inject": 5, "memory_search_limit": 15, "auto_store_messages": true, "enable_fallback_search": true}}, "maximum_quality_3072": {"name": "Maximum Quality 3072D", "description": "Highest quality embeddings with maximum dimensions (resource intensive)", "config": {"embedder_provider": "gemini", "embedder_model": "text-embedding-004", "embedder_dims": 3072, "base_collection_name": "mem0_gemini", "llm_provider": "gemini", "llm_model": "gemini-2.5-flash", "llm_temperature": 0.1, "llm_max_tokens": 1000, "memory_relevance_threshold": 0.4, "max_memories_to_inject": 7, "memory_search_limit": 20, "auto_store_messages": true, "enable_fallback_search": true}}, "openai_compatible": {"name": "OpenAI Compatible", "description": "Configuration for OpenAI embeddings", "config": {"embedder_provider": "openai", "embedder_model": "text-embedding-3-small", "embedder_dims": 1536, "base_collection_name": "mem0_openai", "llm_provider": "openai", "llm_model": "gpt-4", "llm_temperature": 0.1, "llm_max_tokens": 1000, "memory_relevance_threshold": 0.3, "max_memories_to_inject": 4, "memory_search_limit": 12, "auto_store_messages": true, "enable_fallback_search": true}}, "jina_optimized": {"name": "Jina Optimized", "description": "Configuration for Jina embeddings with custom dimensions", "config": {"embedder_provider": "jina", "embedder_model": "jina-embeddings-v3", "embedder_dims": 1024, "base_collection_name": "mem0_jina", "llm_provider": "gemini", "llm_model": "gemini-2.5-flash", "llm_temperature": 0.1, "llm_max_tokens": 1000, "memory_relevance_threshold": 0.25, "max_memories_to_inject": 4, "memory_search_limit": 12, "auto_store_messages": true, "enable_fallback_search": true}}, "debug_mode": {"name": "Debug Mode", "description": "Configuration with extensive logging for troubleshooting", "config": {"embedder_provider": "gemini", "embedder_model": "text-embedding-004", "embedder_dims": 768, "base_collection_name": "mem0_debug", "llm_provider": "gemini", "llm_model": "gemini-2.5-flash", "llm_temperature": 0.1, "llm_max_tokens": 1000, "memory_relevance_threshold": 0.1, "max_memories_to_inject": 2, "memory_search_limit": 5, "auto_store_messages": true, "enable_fallback_search": true, "enable_debug_logging": true}}}, "migration_paths": {"3072_to_768": {"description": "Migrate from 3072 dimensions to optimized 768 dimensions", "source_collections": ["mem0_gemini_3072", "mem0_gemini_gemi_3072"], "target_preset": "optimized_768", "benefits": ["Reduced memory usage", "Faster search performance", "Lower API costs", "Maintained semantic quality"]}, "1536_to_768": {"description": "Migrate from 1536 dimensions to optimized 768 dimensions", "source_collections": ["mem0_gemini_1536", "mem0_gemini_gemi_1536"], "target_preset": "optimized_768", "benefits": ["Better performance", "Lower resource usage", "Faster retrieval"]}}, "recommendations": {"small_projects": "optimized_768", "medium_projects": "high_precision_1536", "large_projects": "maximum_quality_3072", "development": "debug_mode", "production": "optimized_768"}, "performance_comparison": {"768": {"memory_usage": "Low", "search_speed": "Fast", "api_cost": "Low", "semantic_quality": "Good"}, "1536": {"memory_usage": "Medium", "search_speed": "Medium", "api_cost": "Medium", "semantic_quality": "Very Good"}, "3072": {"memory_usage": "High", "search_speed": "Slow", "api_cost": "High", "semantic_quality": "Excellent"}}}