#!/usr/bin/env python3
"""
Test Oracle connection with different methods
"""

import oracledb

def test_oracle_connections():
    print('🔍 Testing Oracle connection methods...')
    
    # Test configurations
    configs = [
        {
            'name': 'Current DSN from valves.json',
            'user': 'ADMIN',
            'password': 'Twilv0zera@123',
            'dsn': '(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))'
        },
        {
            'name': 'TNS Alias only',
            'user': 'ADMIN', 
            'password': 'Twilv0zera@123',
            'dsn': 'swiv8hv5y96iwo2t_high'
        },
        {
            'name': 'Simple TCPS format',
            'user': 'ADMIN',
            'password': 'Twilv0zera@123', 
            'dsn': 'tcps://adb.ap-singapore-2.oraclecloud.com:1522/g872ed23dd62a8b_swiv8hv5y96iwo2t_high.adb.oraclecloud.com'
        }
    ]
    
    working_config = None
    
    for config in configs:
        try:
            print(f'\n🔌 Testing: {config["name"]}')
            print(f'   DSN: {config["dsn"][:80]}...')
            
            connection = oracledb.connect(
                user=config['user'],
                password=config['password'],
                dsn=config['dsn']
            )
            
            print('   ✅ Connection successful!')
            
            cursor = connection.cursor()
            cursor.execute('SELECT 1 FROM DUAL')
            result = cursor.fetchone()
            print(f'   ✅ Query test: {result[0]}')
            
            # Test USER_MEMORY table
            cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name = 'USER_MEMORY'")
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                cursor.execute('SELECT COUNT(*) FROM USER_MEMORY')
                memory_count = cursor.fetchone()[0]
                print(f'   ✅ USER_MEMORY table: {memory_count} memories')
            else:
                print('   ⚠️ USER_MEMORY table not found')
            
            cursor.close()
            connection.close()
            
            print(f'   🎉 {config["name"]} WORKS!')
            working_config = config
            break
            
        except Exception as e:
            print(f'   ❌ Failed: {str(e)[:120]}...')
            continue
    
    if working_config:
        print(f'\n✅ Working configuration found: {working_config["name"]}')
        print(f'📝 DSN: {working_config["dsn"]}')
        return working_config
    else:
        print('\n❌ No working Oracle configuration found')
        print('💡 Possible issues:')
        print('   - Oracle service is down')
        print('   - Credentials have changed')
        print('   - Network/firewall issues')
        print('   - Service name has changed')
        return None

if __name__ == "__main__":
    test_oracle_connections()
