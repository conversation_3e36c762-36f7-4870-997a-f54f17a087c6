"""
Oracle Simple Memory Pipeline
Combines Mem0 768D optimization with Oracle DB storage
Based on proven Simple Mem0 768 + Oracle connection
"""

import json
import logging
import os
import traceback
from typing import List, Optional, Dict, Any

import oracledb
from mem0 import Memory
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Pipeline:
    class Valves(BaseModel):
        # Mem0 Configuration
        mem0_api_key: str = "your-gemini-api-key"
        mem0_base_url: str = "https://generativelanguage.googleapis.com/v1beta/openai/"
        mem0_model: str = "gemini-2.0-flash-exp"
        mem0_embedding_model: str = "text-embedding-004"
        mem0_embedding_dim: int = 768
        
        # Qdrant Configuration
        qdrant_host: str = "qdrant"
        qdrant_port: int = 6333
        qdrant_collection: str = "oracle_simple_memory_768"
        
        # Oracle Configuration
        oracle_user: str = "ADMIN"
        oracle_password: str = "Twilv0zera@123"
        oracle_tns_alias: str = "swiv8hv5y96iwo2t_high"
        oracle_wallet_location: str = "/app/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"
        oracle_wallet_password: str = "Twilv0zera@123"
        
        # Pipeline Settings
        enable_oracle_storage: bool = True
        enable_mem0_storage: bool = True
        memory_relevance_threshold: float = 0.7
        max_memories_to_inject: int = 5

    def __init__(self):
        self.type = "manifold"
        self.id = "oracle-simple-memory"
        self.name = "Oracle Simple Memory"
        self.valves = self.Valves()
        
        # Initialize components
        self.memory = None
        self.oracle_pool = None
        
        logger.info("🚀 Starting Oracle Simple Memory Pipeline...")

    async def on_startup(self):
        """Initialize Mem0 and Oracle connections"""
        try:
            # Initialize Mem0 with 768D optimization
            logger.info("🔧 Initializing Mem0 with 768D optimization...")
            
            mem0_config = {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": self.valves.mem0_model,
                        "api_key": self.valves.mem0_api_key,
                        "base_url": self.valves.mem0_base_url,
                    }
                },
                "embedder": {
                    "provider": "openai",
                    "config": {
                        "model": self.valves.mem0_embedding_model,
                        "api_key": self.valves.mem0_api_key,
                        "base_url": self.valves.mem0_base_url,
                        "embedding_dims": self.valves.mem0_embedding_dim,
                    }
                },
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "collection_name": self.valves.qdrant_collection,
                        "host": self.valves.qdrant_host,
                        "port": self.valves.qdrant_port,
                    }
                }
            }
            
            self.memory = Memory.from_config(mem0_config)
            logger.info("✅ Mem0 768D initialized successfully")
            
            # Initialize Oracle connection
            if self.valves.enable_oracle_storage:
                await self._init_oracle()
            
            logger.info("🎉 Oracle Simple Memory Pipeline ready!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize pipeline: {e}")
            logger.error(traceback.format_exc())

    async def _init_oracle(self):
        """Initialize Oracle connection pool"""
        try:
            logger.info("🔧 Initializing Oracle connection...")
            
            # Create connection pool with proven parameters
            self.oracle_pool = oracledb.create_pool(
                user=self.valves.oracle_user,
                password=self.valves.oracle_password,
                dsn=self.valves.oracle_tns_alias,
                config_dir=self.valves.oracle_wallet_location,
                wallet_location=self.valves.oracle_wallet_location,
                wallet_password=self.valves.oracle_wallet_password,
                min=1, max=3, increment=1,
                ping_interval=60, timeout=300
            )
            
            # Test connection
            conn = self.oracle_pool.acquire()
            cursor = conn.cursor()
            cursor.execute("SELECT USER FROM DUAL")
            user = cursor.fetchone()[0]
            logger.info(f"✅ Oracle connected as: {user}")
            
            # Create memory table if not exists
            await self._create_memory_table(cursor, conn)
            
            cursor.close()
            self.oracle_pool.release(conn)
            
            logger.info("✅ Oracle connection pool initialized")
            
        except Exception as e:
            logger.error(f"❌ Oracle initialization failed: {e}")
            logger.error(traceback.format_exc())
            self.oracle_pool = None

    async def _create_memory_table(self, cursor, conn):
        """Create Oracle memory table if not exists"""
        try:
            # Check if table exists
            cursor.execute("""
                SELECT COUNT(*) FROM USER_TABLES 
                WHERE TABLE_NAME = 'SIMPLE_MEMORY'
            """)
            exists = cursor.fetchone()[0]
            
            if exists == 0:
                logger.info("🔧 Creating SIMPLE_MEMORY table...")
                cursor.execute("""
                    CREATE TABLE SIMPLE_MEMORY (
                        id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                        user_id VARCHAR2(100),
                        session_id VARCHAR2(100),
                        memory_content CLOB,
                        memory_type VARCHAR2(50) DEFAULT 'conversation',
                        relevance_score NUMBER(3,2),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                logger.info("✅ SIMPLE_MEMORY table created")
            else:
                logger.info("✅ SIMPLE_MEMORY table already exists")
                
        except Exception as e:
            logger.error(f"❌ Failed to create memory table: {e}")

    async def on_shutdown(self):
        """Cleanup resources"""
        if self.oracle_pool:
            self.oracle_pool.close()
            logger.info("🔄 Oracle connection pool closed")

    def pipe(self, user_message: str, model_id: str, messages: List[dict], body: dict) -> str:
        """Main pipeline processing"""
        try:
            user_id = body.get("user", {}).get("id", "anonymous")
            session_id = body.get("chat_id", "default")
            
            # Search and inject memories
            relevant_memories = self._search_memories(user_message, user_id)
            
            if relevant_memories:
                memory_context = self._format_memory_context(relevant_memories)
                
                # Inject memory context into system message
                if messages and messages[0].get("role") == "system":
                    messages[0]["content"] += f"\n\n📚 **Relevant Memories:**\n{memory_context}"
                else:
                    messages.insert(0, {
                        "role": "system",
                        "content": f"📚 **Relevant Memories:**\n{memory_context}"
                    })
            
            # Store new memory (async)
            self._store_memory_async(user_message, user_id, session_id)
            
            return user_message
            
        except Exception as e:
            logger.error(f"❌ Pipeline processing error: {e}")
            return user_message

    def _search_memories(self, query: str, user_id: str) -> List[Dict]:
        """Search for relevant memories"""
        try:
            if not self.memory:
                return []
            
            # Search in Mem0
            memories = self.memory.search(
                query=query,
                user_id=user_id,
                limit=self.valves.max_memories_to_inject
            )
            
            # Filter by relevance threshold
            relevant_memories = [
                mem for mem in memories 
                if mem.get('score', 0) >= self.valves.memory_relevance_threshold
            ]
            
            logger.info(f"🔍 Found {len(relevant_memories)} relevant memories")
            return relevant_memories
            
        except Exception as e:
            logger.error(f"❌ Memory search error: {e}")
            return []

    def _format_memory_context(self, memories: List[Dict]) -> str:
        """Format memories for injection"""
        context_parts = []
        for i, memory in enumerate(memories, 1):
            content = memory.get('memory', memory.get('content', ''))
            score = memory.get('score', 0)
            context_parts.append(f"{i}. {content} (relevance: {score:.2f})")
        
        return "\n".join(context_parts)

    def _store_memory_async(self, content: str, user_id: str, session_id: str):
        """Store memory in both Mem0 and Oracle (async)"""
        try:
            # Store in Mem0
            if self.valves.enable_mem0_storage and self.memory:
                self.memory.add(content, user_id=user_id)
                logger.info("✅ Memory stored in Mem0")
            
            # Store in Oracle
            if self.valves.enable_oracle_storage and self.oracle_pool:
                self._store_in_oracle(content, user_id, session_id)
                
        except Exception as e:
            logger.error(f"❌ Memory storage error: {e}")

    def _store_in_oracle(self, content: str, user_id: str, session_id: str):
        """Store memory in Oracle DB"""
        try:
            conn = self.oracle_pool.acquire()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO SIMPLE_MEMORY 
                (user_id, session_id, memory_content, memory_type, relevance_score)
                VALUES (:1, :2, :3, :4, :5)
            """, (user_id, session_id, content, 'conversation', 1.0))
            
            conn.commit()
            cursor.close()
            self.oracle_pool.release(conn)
            
            logger.info("✅ Memory stored in Oracle")
            
        except Exception as e:
            logger.error(f"❌ Oracle storage error: {e}")
