#!/usr/bin/env python3
"""
Debug Re-embed <PERSON>ript: Monitor the re-embedding process step by step
"""

import os
import asyncio
import requests
import json
import time
from typing import List, Dict, Any

print("🔍 Starting debug re-embedding script...")

try:
    from mem0 import AsyncMemory
    print("✅ mem0 imported successfully")
    MEM0_AVAILABLE = True
except ImportError as e:
    print(f"❌ mem0 import failed: {e}")
    MEM0_AVAILABLE = False

# Configuration
QDRANT_HOST = "qdrant"
QDRANT_PORT = 6333
GEMINI_API_KEY = "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"
SOURCE_COLLECTION = "mem0_gemini_3072_fixed"
TARGET_COLLECTION = "mem0_openai_compatible_768"

print(f"📍 Source: {SOURCE_COLLECTION}")
print(f"📍 Target: {TARGET_COLLECTION}")

async def debug_reembed():
    print("\n🚀 Starting debug re-embedding process...")
    
    if not MEM0_AVAILABLE:
        print("❌ mem0 not available, exiting")
        return
    
    # Step 1: Set environment variables
    print("🔧 Setting environment variables...")
    os.environ['OPENAI_BASE_URL'] = 'https://generativelanguage.googleapis.com/v1beta/openai/'
    os.environ['OPENAI_API_KEY'] = GEMINI_API_KEY
    print("✅ Environment variables set")
    
    # Step 2: Check source collection
    print("\n📊 Checking source collection...")
    try:
        response = requests.get(f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{SOURCE_COLLECTION}")
        if response.status_code == 200:
            source_info = response.json()["result"]
            total_count = source_info["points_count"]
            print(f"✅ Source collection: {total_count} memories, {source_info['config']['params']['vectors']['size']} dims")
        else:
            print(f"❌ Source collection not found: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error checking source: {e}")
        return
    
    # Step 3: Check target collection
    print("\n📊 Checking target collection...")
    try:
        response = requests.get(f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}")
        if response.status_code == 200:
            target_info = response.json()["result"]
            existing_count = target_info["points_count"]
            print(f"✅ Target collection: {existing_count} memories, {target_info['config']['params']['vectors']['size']} dims")
        else:
            print(f"❌ Target collection not found: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error checking target: {e}")
        return
    
    # Step 4: Setup memory client
    print("\n📦 Setting up memory client...")
    try:
        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": QDRANT_HOST,
                    "port": QDRANT_PORT,
                    "collection_name": TARGET_COLLECTION,
                },
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gemini-2.5-flash",
                    "temperature": 0.1,
                    "max_tokens": 1000,
                },
            },
            "embedder": {
                "provider": "gemini",
                "config": {
                    "api_key": GEMINI_API_KEY,
                    "model": "text-embedding-004",
                    "embedding_dims": 768,
                },
            },
        }
        
        print("🔄 Creating AsyncMemory client...")
        memory_client = await AsyncMemory.from_config(config)
        print("✅ Memory client created successfully")
        
    except Exception as e:
        print(f"❌ Error creating memory client: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 5: Get first batch of memories
    print("\n📤 Getting first batch of memories...")
    try:
        response = requests.post(
            f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{SOURCE_COLLECTION}/points/scroll",
            json={"limit": 5, "with_payload": True, "with_vector": False}
        )
        
        if response.status_code == 200:
            data = response.json()
            points = data["result"]["points"]
            print(f"✅ Got {len(points)} memories for testing")
            
            # Step 6: Re-embed batch
            print("\n🔄 Starting re-embedding...")
            for i, point in enumerate(points):
                try:
                    content = point["payload"].get("content", "")
                    user_id = point["payload"].get("user_id", "default_user")
                    
                    if content:
                        print(f"\n📝 Memory {i+1}/{len(points)}:")
                        print(f"   Content: {content[:100]}...")
                        print(f"   User ID: {user_id}")
                        
                        print("🔄 Adding to 768D collection...")
                        result = await memory_client.add(
                            messages=content,
                            user_id=user_id,
                            metadata={
                                "original_id": point["id"],
                                "migrated_from": SOURCE_COLLECTION,
                                "reembedded_768d": True,
                                "debug_batch": True
                            }
                        )
                        
                        print(f"✅ Memory {i+1} re-embedded successfully")
                        print(f"📊 Result: {result}")
                        
                        # Rate limiting
                        if i < len(points) - 1:
                            print("⏳ Waiting 3 seconds...")
                            await asyncio.sleep(3)
                    else:
                        print(f"⚠️  Memory {i+1} has no content, skipping")
                        
                except Exception as e:
                    print(f"❌ Error re-embedding memory {i+1}: {e}")
                    continue
            
            # Step 7: Check final stats
            print("\n📊 Checking final collection stats...")
            response = requests.get(f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}")
            if response.status_code == 200:
                final_info = response.json()["result"]
                final_count = final_info["points_count"]
                print(f"✅ Target collection now has: {final_count} memories")
                print(f"📈 Added: {final_count - existing_count} new memories")
            
            print("\n🎉 Debug re-embedding completed successfully!")
            print("💡 Process is working correctly")
            
            # Ask if user wants to continue with full re-embedding
            print(f"\n❓ Ready to re-embed all {total_count} memories?")
            print("   This will take approximately 1-2 hours with rate limiting")
            print("   You can run the full script: reembed_to_768_openai_compatible.py")
            
        else:
            print(f"❌ Error getting memories: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in re-embedding process: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_reembed())
