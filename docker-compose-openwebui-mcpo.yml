version: '3.8'

services:
  open-webui:
    image: ghcr.io/open-webui/open-webui:v0.6.21
    container_name: open-webui-mcpo
    ports:
      - "3000:8080"
      - "3005:8080"
    environment:
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - WEBUI_SECRET_KEY=your-secret-key-here
      - ENABLE_PIPELINES=true
      - PIPELINES_URL=http://pipelines:9099
      - PIPELINES_API_KEY=0p3n-w3bu!
      - ENABLE_RAG_HYBRID_SEARCH=true
      - ENABLE_RAG_WEB_LOADER=true
      - RAG_EMBEDDING_ENGINE=google
      - RAG_VECTOR_DB=qdrant
      - RAG_QDRANT_URL=http://qdrant:6333
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - RAG_EMBEDDING_MODEL=models/text-embedding-004
      - ENABLE_OPENAI_API=true
      - OPENAI_API_BASE_URL=http://host.docker.internal:11434/v1
      - OPENAI_API_KEY=ollama
      - PYTHONPATH=/app/mcp-servers
      - GEMINI_API_KEY=AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM
      - ENABLE_MCPO=true
      - MCPO_CONFIG_PATH=/app/mcpo_config.json
    volumes:
      - ./mem0-owui/webui-data:/app/backend/data
      - ./mem0-owui/mcp-integration/config/openwebui_mcpo_config.json:/app/mcpo_config.json:ro
      - ./mem0-owui/mcp-integration/servers:/app/mcp-servers:ro
    networks:
      - acca-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  pipelines:
    image: ghcr.io/open-webui/pipelines:latest
    container_name: pipelines
    ports:
      - "9099:9099"
    volumes:
      - ./mem0-owui/webui-data/pipelines:/app/pipelines
    networks:
      - acca-network
    restart: unless-stopped

  mcpo-server:
    image: python:3.11-slim
    container_name: mcpo-server
    ports:
      - "5000:5000"
    working_dir: /app
    volumes:
      - ./mem0-owui:/app
    networks:
      - acca-network
    environment:
      - PYTHONPATH=/app
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    command: >
      bash -c "
        echo 'Installing Python dependencies...' &&
        pip install --no-cache-dir --upgrade pip &&
        pip install --no-cache-dir fastapi uvicorn &&
        echo 'Starting Complete MCPO server...' &&
        python3 mcpo_complete_server.py
      "
    restart: unless-stopped
    depends_on:
      - open-webui
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  acca-network:
    external: true

volumes:
  acca_open_webui_data:
    external: true